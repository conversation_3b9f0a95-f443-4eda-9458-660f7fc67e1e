import React from 'react';

const ActivityLogStats = ({ filters }) => {
  // Mock stats data - in real app, this would be calculated based on filters
  const stats = [
    {
      title: 'Total Activities',
      value: '2,847',
      change: '+12%',
      changeType: 'increase',
      icon: 'icon-activity',
      color: 'primary'
    },
    {
      title: 'Failed Actions',
      value: '23',
      change: '-8%',
      changeType: 'decrease',
      icon: 'icon-alert-triangle',
      color: 'danger'
    },
    {
      title: 'User Actions',
      value: '1,924',
      change: '+15%',
      changeType: 'increase',
      icon: 'icon-users',
      color: 'success'
    },
    {
      title: 'System Actions',
      value: '923',
      change: '+5%',
      changeType: 'increase',
      icon: 'icon-cpu',
      color: 'info'
    }
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      primary: 'bg-primary-100 text-primary-600',
      danger: 'bg-danger-100 text-danger-600',
      success: 'bg-success-100 text-success-600',
      info: 'bg-info-100 text-info-600'
    };
    return colorMap[color] || colorMap.primary;
  };

  const getChangeColorClass = (changeType) => {
    return changeType === 'increase' ? 'text-success-600' : 'text-danger-600';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg border border-border-color p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getColorClasses(stat.color)}`}>
                <span className={`${stat.icon} text-lg`} />
              </div>
              <div>
                <p className="text-sm text-gray-500">{stat.title}</p>
                <p className="text-xl font-bold text-dark-500">{stat.value}</p>
              </div>
            </div>
            <div className="text-right">
              <span className={`text-sm font-medium ${getChangeColorClass(stat.changeType)}`}>
                {stat.change}
              </span>
              <p className="text-xs text-gray-400">vs last week</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ActivityLogStats;
