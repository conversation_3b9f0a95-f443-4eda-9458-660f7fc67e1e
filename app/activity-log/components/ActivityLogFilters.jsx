import React, { useState } from 'react';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';

const ActivityLogFilters = ({ filters, onFilterChange, onClearFilters }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const moduleOptions = [
    { value: '', label: 'All Modules' },
    { value: 'product', label: 'Product Management' },
    { value: 'vendor', label: 'Vendor Management' },
    { value: 'order', label: 'Order Management' },
    { value: 'inventory', label: 'Inventory Management' },
    { value: 'settings', label: 'Settings' },
    { value: 'user', label: 'User Management' },
    { value: 'billing', label: 'Billing' },
    { value: 'integration', label: 'Integrations' }
  ];

  const actionTypeOptions = [
    { value: '', label: 'All Actions' },
    { value: 'create', label: 'Create' },
    { value: 'edit', label: 'Edit' },
    { value: 'delete', label: 'Delete' },
    { value: 'approve', label: 'Approve' },
    { value: 'reject', label: 'Reject' },
    { value: 'login', label: 'Login' },
    { value: 'logout', label: 'Logout' },
    { value: 'export', label: 'Export' },
    { value: 'import', label: 'Import' },
    { value: 'sync', label: 'Sync' }
  ];

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'success', label: 'Success' },
    { value: 'failed', label: 'Failed' }
  ];

  const handleInputChange = (field, value) => {
    onFilterChange({ [field]: value });
  };

  const handleDateRangeChange = (field, value) => {
    onFilterChange({ 
      dateRange: { 
        ...filters.dateRange, 
        [field]: value 
      } 
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.performedBy) count++;
    if (filters.module) count++;
    if (filters.actionType) count++;
    if (filters.status) count++;
    if (filters.entityName) count++;
    if (filters.ipAddress) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <div className="space-y-4">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium text-dark-500">Filters</h3>
          {activeFiltersCount > 0 && (
            <span className="px-2 py-1 text-xs font-medium bg-primary-100 text-primary-600 rounded-full">
              {activeFiltersCount} active
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={onClearFilters}
            className="text-sm text-gray-500 hover:text-gray-700"
            disabled={activeFiltersCount === 0}
          >
            Clear All
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-1 text-sm text-primary-600 hover:text-primary-700"
          >
            {isExpanded ? 'Less Filters' : 'More Filters'}
            <span className={`icon icon-chevron-${isExpanded ? 'up' : 'down'} text-xs`} />
          </button>
        </div>
      </div>

      {/* Basic Filters - Always Visible */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="flex flex-col">
          <label className="text-xs font-medium text-gray-600 mb-1">Date From</label>
          <input
            type="date"
            value={filters.dateRange.start}
            onChange={(e) => handleDateRangeChange('start', e.target.value)}
            className="form-control text-sm"
          />
        </div>
        <div className="flex flex-col">
          <label className="text-xs font-medium text-gray-600 mb-1">Date To</label>
          <input
            type="date"
            value={filters.dateRange.end}
            onChange={(e) => handleDateRangeChange('end', e.target.value)}
            className="form-control text-sm"
          />
        </div>
        <div className="flex flex-col">
          <label className="text-xs font-medium text-gray-600 mb-1">Module</label>
          <select
            value={filters.module}
            onChange={(e) => handleInputChange('module', e.target.value)}
            className="form-control text-sm"
          >
            {moduleOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="flex flex-col">
          <label className="text-xs font-medium text-gray-600 mb-1">Action Type</label>
          <select
            value={filters.actionType}
            onChange={(e) => handleInputChange('actionType', e.target.value)}
            className="form-control text-sm"
          >
            {actionTypeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters - Expandable */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-border-color">
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-600 mb-1">Performed By</label>
            <input
              type="text"
              value={filters.performedBy}
              onChange={(e) => handleInputChange('performedBy', e.target.value)}
              placeholder="User name or email"
              className="form-control text-sm"
            />
          </div>
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-600 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="form-control text-sm"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-600 mb-1">Entity Name/ID</label>
            <input
              type="text"
              value={filters.entityName}
              onChange={(e) => handleInputChange('entityName', e.target.value)}
              placeholder="Product name, Order ID, etc."
              className="form-control text-sm"
            />
          </div>
          <div className="flex flex-col">
            <label className="text-xs font-medium text-gray-600 mb-1">IP Address</label>
            <input
              type="text"
              value={filters.ipAddress}
              onChange={(e) => handleInputChange('ipAddress', e.target.value)}
              placeholder="***********"
              className="form-control text-sm"
            />
          </div>
        </div>
      )}

      {/* Quick Filter Buttons */}
      <div className="flex flex-wrap gap-2 pt-2">
        <button
          onClick={() => onFilterChange({ actionType: 'login' })}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            filters.actionType === 'login'
              ? 'bg-primary-100 text-primary-700 border-primary-200'
              : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
          }`}
        >
          Login Activities
        </button>
        <button
          onClick={() => onFilterChange({ status: 'failed' })}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            filters.status === 'failed'
              ? 'bg-danger-100 text-danger-700 border-danger-200'
              : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
          }`}
        >
          Failed Actions
        </button>
        <button
          onClick={() => onFilterChange({ module: 'product' })}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            filters.module === 'product'
              ? 'bg-success-100 text-success-700 border-success-200'
              : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
          }`}
        >
          Product Changes
        </button>
        <button
          onClick={() => {
            const today = new Date().toISOString().split('T')[0];
            onFilterChange({ dateRange: { start: today, end: today } });
          }}
          className={`px-3 py-1 text-xs rounded-full border transition-colors ${
            filters.dateRange.start === new Date().toISOString().split('T')[0]
              ? 'bg-info-100 text-info-700 border-info-200'
              : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
          }`}
        >
          Today
        </button>
      </div>
    </div>
  );
};

export default ActivityLogFilters;
