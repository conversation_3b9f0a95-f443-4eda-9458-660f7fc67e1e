import React from 'react';
import BaseModal from '@/app/components/modals/BaseModal';

const ActivityLogDetailModal = ({ log, isOpen, onClose }) => {
  if (!log) return null;

  const getStatusBadge = (status) => {
    const statusClasses = {
      'Success': 'bg-success-100 text-success-800',
      'Failed': 'bg-danger-100 text-danger-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const getRoleBadge = (role) => {
    const roleClasses = {
      'Admin': 'bg-primary-100 text-primary-800',
      'Vendor': 'bg-warning-100 text-warning-800',
      'System': 'bg-info-100 text-info-800'
    };
    return roleClasses[role] || 'bg-gray-100 text-gray-800';
  };

  const formatDateTime = (timestamp) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      time: date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      })
    };
  };

  const renderChanges = (changes) => {
    if (!changes || typeof changes !== 'object') return null;

    return (
      <div className="space-y-3">
        {Object.entries(changes).map(([key, value]) => (
          <div key={key} className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-2 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}
            </div>
            {typeof value === 'object' && value.from !== undefined && value.to !== undefined ? (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-500">From:</span>
                  <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                    {value.from || 'null'}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-500">To:</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                    {value.to}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-600">
                {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const { date, time } = formatDateTime(log.timestamp);

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Activity Log Details"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <div className="flex items-start justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-lg font-medium">
              {log.performedBy.charAt(0).toUpperCase()}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{log.performedBy}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getRoleBadge(log.userRole)}`}>
                  {log.userRole}
                </span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(log.status)}`}>
                  {log.status}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">{date}</div>
            <div className="text-sm text-gray-500">{time}</div>
          </div>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Log ID</label>
              <p className="text-sm font-mono text-gray-900">{log.id}</p>
            </div>
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Action Type</label>
              <p className="text-sm text-gray-900">{log.actionType}</p>
            </div>
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Module</label>
              <p className="text-sm text-gray-900">{log.module}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Entity Name</label>
              <p className="text-sm text-gray-900">{log.entityName}</p>
            </div>
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Entity ID</label>
              <p className="text-sm font-mono text-gray-900">{log.entityId}</p>
            </div>
            <div>
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">IP Address</label>
              <p className="text-sm font-mono text-gray-900">{log.ipAddress}</p>
            </div>
          </div>
        </div>

        {/* Action Summary */}
        <div>
          <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 block">Action Summary</label>
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-900">{log.actionSummary}</p>
          </div>
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Device/Browser Info</label>
            <p className="text-sm text-gray-900">{log.deviceInfo}</p>
          </div>
          <div>
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Location</label>
            <p className="text-sm text-gray-900">{log.location}</p>
          </div>
        </div>

        {/* Detailed Changes */}
        {log.details && log.details.changes && (
          <div>
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 block">Detailed Changes</label>
            {renderChanges(log.details.changes)}
          </div>
        )}

        {/* Metadata */}
        {log.details && log.details.metadata && (
          <div>
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 block">Metadata</label>
            <div className="bg-gray-50 p-3 rounded-lg">
              <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(log.details.metadata, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Additional Details for Failed Actions */}
        {log.status === 'Failed' && log.details && (
          <div>
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3 block">Failure Details</label>
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
              {log.details.reason && (
                <div className="mb-2">
                  <span className="text-sm font-medium text-red-800">Reason: </span>
                  <span className="text-sm text-red-700">{log.details.reason}</span>
                </div>
              )}
              {log.details.attempts && (
                <div className="mb-2">
                  <span className="text-sm font-medium text-red-800">Attempts: </span>
                  <span className="text-sm text-red-700">{log.details.attempts}</span>
                </div>
              )}
              {log.details.accountLocked !== undefined && (
                <div>
                  <span className="text-sm font-medium text-red-800">Account Locked: </span>
                  <span className="text-sm text-red-700">{log.details.accountLocked ? 'Yes' : 'No'}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Modal Footer */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
        <button
          onClick={() => {
            // Export individual log details
            const dataStr = JSON.stringify(log, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `activity-log-${log.id}.json`;
            link.click();
            URL.revokeObjectURL(url);
          }}
          className="btn btn-outline-primary"
        >
          <span className="icon icon-download mr-2" />
          Export Details
        </button>
        <button
          onClick={onClose}
          className="btn btn-primary"
        >
          Close
        </button>
      </div>
    </BaseModal>
  );
};

export default ActivityLogDetailModal;
