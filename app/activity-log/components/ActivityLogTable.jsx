import React, { useState, useEffect } from 'react';
import DataTable from 'react-data-table-component';
import SortIcon from '@/app/components/table/SortIcon';
import ActivityLogDetailModal from './ActivityLogDetailModal';

const ActivityLogTable = ({ filters, isLoading, setIsLoading }) => {
  const [activityLogs, setActivityLogs] = useState([]);
  const [selectedLog, setSelectedLog] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Mock data - in real app, this would come from API
  const mockActivityLogs = [
    {
      id: 'LOG-001',
      timestamp: '2024-01-20 14:30:25',
      performedBy: '<PERSON>',
      userRole: 'Admin',
      actionType: 'Edit',
      module: 'Product Management',
      entityName: 'MacBook Pro 14"',
      entityId: 'PROD-001',
      actionSummary: 'Updated price from ₹150,000 to ₹145,000',
      ipAddress: '************',
      deviceInfo: 'Chrome 120.0 on Windows 10',
      location: 'Mumbai, India',
      status: 'Success',
      details: {
        changes: {
          price: { from: '₹150,000', to: '₹145,000' },
          updatedAt: { from: '2024-01-20 14:25:00', to: '2024-01-20 14:30:25' }
        },
        metadata: {
          sessionId: 'sess_abc123',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      }
    },
    {
      id: 'LOG-002',
      timestamp: '2024-01-20 14:15:12',
      performedBy: 'TechPro Supplies',
      userRole: 'Vendor',
      actionType: 'Create',
      module: 'Product Management',
      entityName: 'iPhone 15 Pro',
      entityId: 'PROD-002',
      actionSummary: 'Created new product listing',
      ipAddress: '*************',
      deviceInfo: 'Safari 17.0 on macOS',
      location: 'Delhi, India',
      status: 'Success',
      details: {
        changes: {
          name: { from: null, to: 'iPhone 15 Pro' },
          price: { from: null, to: '₹134,900' },
          category: { from: null, to: 'Smartphones' }
        }
      }
    },
    {
      id: 'LOG-003',
      timestamp: '2024-01-20 13:45:33',
      performedBy: 'System',
      userRole: 'System',
      actionType: 'Sync',
      module: 'Inventory Management',
      entityName: 'Stock Sync',
      entityId: 'SYNC-001',
      actionSummary: 'Synchronized inventory levels from warehouse',
      ipAddress: '127.0.0.1',
      deviceInfo: 'System Process',
      location: 'Server',
      status: 'Success',
      details: {
        changes: {
          productsUpdated: 45,
          stockAdjustments: 23,
          lowStockAlerts: 5
        }
      }
    },
    {
      id: 'LOG-004',
      timestamp: '2024-01-20 13:20:15',
      performedBy: '<EMAIL>',
      userRole: 'Vendor',
      actionType: 'Login',
      module: 'Authentication',
      entityName: 'Login Attempt',
      entityId: 'AUTH-001',
      actionSummary: 'Failed login attempt - Invalid credentials',
      ipAddress: '************',
      deviceInfo: 'Chrome 119.0 on Android',
      location: 'Bangalore, India',
      status: 'Failed',
      details: {
        reason: 'Invalid password',
        attempts: 3,
        accountLocked: false
      }
    },
    {
      id: 'LOG-005',
      timestamp: '2024-01-20 12:55:42',
      performedBy: 'Jane Smith',
      userRole: 'Admin',
      actionType: 'Approve',
      module: 'Vendor Management',
      entityName: 'ElectroWorld Store',
      entityId: 'VENDOR-003',
      actionSummary: 'Approved new vendor application',
      ipAddress: '************',
      deviceInfo: 'Firefox 121.0 on Ubuntu',
      location: 'Chennai, India',
      status: 'Success',
      details: {
        changes: {
          status: { from: 'Pending', to: 'Approved' },
          approvedBy: 'Jane Smith',
          approvalDate: '2024-01-20 12:55:42'
        }
      }
    }
  ];

  useEffect(() => {
    // Simulate API call
    setIsLoading(true);
    setTimeout(() => {
      setActivityLogs(mockActivityLogs);
      setIsLoading(false);
    }, 1000);
  }, [filters, setIsLoading]);

  const handleViewDetails = (log) => {
    setSelectedLog(log);
    setShowDetailModal(true);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      'Success': 'bg-success-100 text-success-800',
      'Failed': 'bg-danger-100 text-danger-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  const getRoleBadge = (role) => {
    const roleClasses = {
      'Admin': 'bg-primary-100 text-primary-800',
      'Vendor': 'bg-warning-100 text-warning-800',
      'System': 'bg-info-100 text-info-800'
    };
    return roleClasses[role] || 'bg-gray-100 text-gray-800';
  };

  const getActionTypeIcon = (actionType) => {
    const iconMap = {
      'Create': 'icon-plus',
      'Edit': 'icon-edit',
      'Delete': 'icon-trash',
      'Approve': 'icon-check',
      'Reject': 'icon-x',
      'Login': 'icon-log-in',
      'Logout': 'icon-log-out',
      'Sync': 'icon-refresh-cw',
      'Export': 'icon-download',
      'Import': 'icon-upload'
    };
    return iconMap[actionType] || 'icon-activity';
  };

  const columns = [
    {
      name: 'Date & Time',
      selector: row => row.timestamp,
      sortable: true,
      width: '160px',
      cell: row => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {new Date(row.timestamp).toLocaleDateString()}
          </div>
          <div className="text-gray-500">
            {new Date(row.timestamp).toLocaleTimeString()}
          </div>
        </div>
      )
    },
    {
      name: 'User',
      selector: row => row.performedBy,
      sortable: true,
      width: '180px',
      cell: row => (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
            {row.performedBy.charAt(0).toUpperCase()}
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">{row.performedBy}</div>
            <span className={`inline-flex px-2 py-0.5 text-xs font-medium rounded-full ${getRoleBadge(row.userRole)}`}>
              {row.userRole}
            </span>
          </div>
        </div>
      )
    },
    {
      name: 'Action',
      selector: row => row.actionType,
      sortable: true,
      width: '120px',
      cell: row => (
        <div className="flex items-center gap-2">
          <span className={`${getActionTypeIcon(row.actionType)} text-gray-600`} />
          <span className="text-sm font-medium text-gray-900">{row.actionType}</span>
        </div>
      )
    },
    {
      name: 'Module',
      selector: row => row.module,
      sortable: true,
      width: '150px',
      cell: row => (
        <span className="text-sm text-gray-900">{row.module}</span>
      )
    },
    {
      name: 'Entity Affected',
      selector: row => row.entityName,
      sortable: true,
      grow: 2,
      cell: row => (
        <div>
          <div className="text-sm font-medium text-gray-900">{row.entityName}</div>
          <div className="text-xs text-gray-500">{row.entityId}</div>
        </div>
      )
    },
    {
      name: 'Summary',
      selector: row => row.actionSummary,
      sortable: false,
      grow: 3,
      cell: row => (
        <div className="text-sm text-gray-700 line-clamp-2">
          {row.actionSummary}
        </div>
      )
    },
    {
      name: 'IP Address',
      selector: row => row.ipAddress,
      sortable: true,
      width: '160px',
      cell: row => (
        <span className="text-sm font-mono text-gray-300">{row.ipAddress}</span>
      )
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(row.status)}`}>
          {row.status}
        </span>
      )
    },
    {
      name: 'Actions',
      cell: row => (
        <button
          onClick={() => handleViewDetails(row)}
          className="flex items-center gap-1 text-primary-600 hover:text-primary-900 text-sm font-medium"
        >
          <span className="icon icon-eye text-xs" />
        </button>
      )
    }
  ];

  const customTableStyles = {
    headRow: {
      style: {
        backgroundColor: '#f9fafb',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
        minHeight: '48px'
      },
    },
    headCells: {
      style: {
        paddingLeft: '16px',
        paddingRight: '16px',
        color: '#6b7280',
        fontWeight: '500',
        textTransform: 'uppercase',
        fontSize: '11px',
        letterSpacing: '0.05em'
      },
    },
    cells: {
      style: {
        paddingLeft: '16px',
        paddingRight: '16px',
      },
    },
    rows: {
      style: {
        minHeight: '60px',
        '&:hover': {
          backgroundColor: '#f9fafb',
        }
      },
    },
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={activityLogs}
        customStyles={customTableStyles}
        pagination
        paginationPerPage={10}
        paginationRowsPerPageOptions={[10, 25, 50, 100]}
        highlightOnHover
        pointerOnHover
        className="custom-table auto-height-table"
        sortIcon={<SortIcon />}
        defaultSortFieldId={1}
        defaultSortAsc={false}
        progressPending={isLoading}
        progressComponent={
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        }
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
        }}
        noDataComponent={
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <span className="icon icon-activity text-gray-400 text-2xl" />
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">No activity logs found</h3>
            <p className="text-sm text-gray-500">Try adjusting your filters to see more results.</p>
          </div>
        }
      />

      {/* Detail Modal */}
      {showDetailModal && selectedLog && (
        <ActivityLogDetailModal
          log={selectedLog}
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
        />
      )}
    </>
  );
};

export default ActivityLogTable;
