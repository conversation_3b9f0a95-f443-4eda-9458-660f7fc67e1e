'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import ActivityLogTable from './components/ActivityLogTable';
import ActivityLogFilters from './components/ActivityLogFilters';
import ActivityLogStats from './components/ActivityLogStats';

export default function ActivityLogPage() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '' },
    performedBy: '',
    module: '',
    actionType: '',
    status: '',
    entityName: '',
    ipAddress: ''
  });

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Activity Log' },
  ];

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setFilters({
      dateRange: { start: '', end: '' },
      performedBy: '',
      module: '',
      actionType: '',
      status: '',
      entityName: '',
      ipAddress: ''
    });
  };

  const handleExportLogs = () => {
    console.log('Exporting logs with filters:', filters);
    // Implement export functionality
  };

  return (
    <PrivateLayout>
      <div className="flex bg-surface-100 rounded-xl w-[calc(100% - 12px)] mt-[60px]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}>
          {/* Enhanced Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Activity Log</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          <div className="w-full">
            {/* Activity Stats */}
            <div className="mb-6">
              <ActivityLogStats filters={filters} />
            </div>

            {/* Main Content */}
            <div className="bg-white rounded-xl">
              {/* Filters Section */}
              <div className="p-4 border-b border-border-color">
                <ActivityLogFilters
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onClearFilters={handleClearFilters}
                />
                <div className="flex items-center gap-3">
                  <button
                    onClick={handleExportLogs}
                    className="btn btn-outline-primary"
                  >
                    <span className="icon icon-download mr-2" />
                    Export Logs
                  </button>
                  <button
                    onClick={handleClearFilters}
                    className="btn btn-outline-gray"
                  >
                    <span className="icon icon-refresh mr-2" />
                    Clear Filters
                  </button>
                </div>
              </div>

              {/* Activity Log Table */}
              <div className="activity-log-table">
                <ActivityLogTable
                  filters={filters}
                  isLoading={isLoading}
                  setIsLoading={setIsLoading}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
