'use client';

import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ManufacturerBrands from '../components/master-management/ManufacturerBrands';
import FilterField from '../components/table/FilterField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '../components/table/SortableItem';

export default function Page() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [displayProperties, setDisplayProperties] = useState({
    'Manufacturer Name': true,
    'Country of Origin': true,
    'Status': true,
  });

  const items = useMemo(() => Object.keys(displayProperties), [displayProperties]);

  const handleClickOutside = useCallback((event) => {
    if (displayMenuRef.current && !displayMenuRef.current.contains(event.target)) {
      setIsDisplayMenuOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [handleClickOutside]);

  // ✅ Drag reorder logic
  const handleDragEnd = useCallback((event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    setDisplayProperties(prev => {
      const keys = Object.keys(prev);
      const oldIndex = keys.indexOf(active.id);
      const newIndex = keys.indexOf(over.id);

      const reordered = [...keys];
      reordered.splice(oldIndex, 1);
      reordered.splice(newIndex, 0, active.id);

      const newState = {};
      reordered.forEach(key => {
        newState[key] = prev[key];
      });
      return newState;
    });
  }, []);

  const breadcrumbItems = useMemo(() => [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Masters' },
    { label: 'Manufacturer Brands' },
  ], []);

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Manufacturer Brands</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex justify-end gap-3">
              <button
                type="button"
                className="btn btn-primary flex items-center gap-2"
                onClick={() => setIsModalOpen(true)}
              >
                <span className="icon icon-plus text-base" />
                Add Manufacturer Brand
              </button>
            </div>
          </div>

          <div className="rounded-xl">
            {/* Filter filed */}
            <FilterField
              filterText={filterText}
              setFilterText={setFilterText}
              isSearchFilterOpen={isSearchFilterOpen}
              setIsSearchFilterOpen={setIsSearchFilterOpen}
              isDisplayMenuOpen={isDisplayMenuOpen}
              setIsDisplayMenuOpen={setIsDisplayMenuOpen}
              displayMenuRef={displayMenuRef}
            >
              <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
                <DndContext
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={items}
                    strategy={verticalListSortingStrategy}
                  >
                    <ul className="space-y-1">
                      {items.map((key) => (
                        <SortableItem
                          key={key}
                          id={key}
                          value={key}
                          checked={displayProperties[key]}
                          onChange={() =>
                            setDisplayProperties((prev) => ({
                              ...prev,
                              [key]: !prev[key],
                            }))
                          }
                        />
                      ))}
                    </ul>
                  </SortableContext>
                </DndContext>
              </div>
            </FilterField>

            <ManufacturerBrands
              isModalOpen={isModalOpen}
              setIsModalOpen={setIsModalOpen}
            />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
