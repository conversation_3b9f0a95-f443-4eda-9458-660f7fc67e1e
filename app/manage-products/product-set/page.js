'use client';
import { Formik } from 'formik';
import Link from 'next/link';
import React, { useEffect, useRef, useState } from 'react';
import ProductStepper from '../../components/create-product-listing/ProductStepper'
import GeneralInformation from '../../components/create-product-listing/GeneralInformation';
import ProductDescription from '../../components/create-product-listing/ProductDescription';
import PricingInventory from '../../components/create-product-listing/PricingInventory';
import ShippingLogistics from '../../components/create-product-listing/ShippingLogistics';
import MetaInformation from '../../components/create-product-listing/MetaInformation';
import ProductPolicies from '../../components/create-product-listing/ProductPolicies';
import { PRODUCT_SCHEMA } from '@/utils/schema';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import BaseModal from '../../components/modals/BaseModal';
import StepNavigationButtons from '@/app/components/create-product-listing/StepNavigationButtons';
import { useRouter } from 'next/navigation';
import { LOGIN } from '@/routes/urls';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUserAsync } from '@/store/api/authApi';
import { getProductDetails } from '@/store/api/productApi';
import { resetProductState } from '@/store/actions/userActions';
import { showErrorToast } from '@/utils/function';
import Pricing from '@/app/components/create-product-listing/Pricing';
import PromotionDiscounts from '@/app/components/create-product-listing/PromotionDiscounts';
import { ProductType } from '@/utils/constant';

const ProductSet = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showSubmittedModal, setShowSubmittedModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pendingSections, setPendingSections] = useState([])
  const [showPopup, setShowPopup] = useState(false)
  const dispatch = useDispatch();
  const { productId } = useSelector((state) => state.product);
  const allowNavRef = useRef(false)
  const nextUrlRef = useRef(null)
  const currentUrlRef = useRef(null);   
  const route = useRouter();

  useEffect(() => {
    // Set the current URL when component mounts
    currentUrlRef.current = window.location.pathname + window.location.search;

    // Push a dummy state to trap browser back button
    history.pushState(null, "", currentUrlRef.current);

    const handlePopState = () => {
      if (!allowNavRef.current) {
        nextUrlRef.current = document.referrer || "/";
        setShowPopup(true);

        // Push current URL again to prevent navigation
        history.pushState(null, "", currentUrlRef.current);
      }
    };

    const originalPush = route.push;
    const customPush = (url, options) => {
      if (!allowNavRef.current) {
        nextUrlRef.current = url;
        setShowPopup(true);
        return;
      }
      return originalPush(url, options);
    };

    // Override route.push with custom behavior
    route.push = customPush;

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
      route.push = originalPush; // Restore original push method
    };
  }, [route]);
  
useEffect (()=>{

if(pendingSections.length){
  showErrorToast("Please fill up pending sections")
}
},[pendingSections])



  const handleNext = (newPendingSections = pendingSections) => {
    if (!completedSteps.includes(activeStep)) {
      setCompletedSteps([...completedSteps, activeStep]);
    }
    setActiveStep((prev) => Math.min(prev + 1, steps.length - 1));
  
    if (activeStep === steps.length - 1 && newPendingSections.length === 0) {
      setShowSubmittedModal(true);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => Math.max(prev - 1, 0));
  };

  const handleStepClick = (stepIndex) => {
    // if (completedSteps.includes(stepIndex) || stepIndex <= activeStep) {
    //   setActiveStep(stepIndex);
    // }
    if(completedSteps.includes(0)){

      setActiveStep(stepIndex)
    }

  };

  useEffect(() => {
    // Initial render
    GetProductData(true);
  }, []);
  
  useEffect(() => {
    if (activeStep !== null) {
      // Called on step change (but doesn't update step)
      GetProductData(false);
    }
  }, [activeStep]);
  
  const GetProductData = async (shouldUpdateStep = false) => {
    if (productId) {
      const { payload } = await dispatch(getProductDetails(productId));
      if (payload.ok) {
        const adjustedSteps = (payload?.data?.product_steps || []).map(step => step - 1);
        setCompletedSteps(adjustedSteps);
  
        if (shouldUpdateStep) {
          const fullSequence = [1, 2, 3, 4, 5, 6, 7];
          const completedOneBased = adjustedSteps.map(step => step + 1); 
          const missingStep = fullSequence.find(step => !completedOneBased.includes(step)); 
  
          setActiveStep(missingStep ? missingStep - 1 : 6);
        }
      }
    } else {
      setActiveStep(0);
    }
  };



  const steps = [
    { title: 'General Information', component: <GeneralInformation module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } />  },
    { title: 'Product Description', component: <ProductDescription module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } /> },
    { title: 'Pricing and Inventory', component: <Pricing module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } /> },
    { title: 'Promotions and Discounts', component: <PromotionDiscounts module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } /> },
    { title: 'Shipping and Logistics', component: <ShippingLogistics module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } /> },
    { title: 'Product Policies', component: <ProductPolicies module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
        activeStep={activeStep}
        handleBack={handleBack}
        handleNext={handleNext}
        setIsModalOpen={setIsModalOpen}
        setShowSubmittedModal={setShowSubmittedModal}
      />
    } /> },
    { title: 'Meta Information', component: <MetaInformation module= {ProductType?.BUNDLE} navigationComponent={
      <StepNavigationButtons
      activeStep={activeStep}
      handleBack={handleBack}
      handleNext={handleNext}
      setIsModalOpen={setIsModalOpen}
      setShowSubmittedModal={setShowSubmittedModal}
      setPendingSections={setPendingSections}
      module={ProductType?.BUNDLE}
      />
    } /> }
  ];

  return (
    <PrivateLayout>
      <div className="flex bg-white mt-[58px] w-full relative">
        {/* Sidebar */}
        <div className="fixed top-16 transition-base z-50 left-3 h-[calc(100dvh-58px)] max-w-[250px] 2xl:max-w-[352px] p-6 2xl:p-[55] 2xl:pb-5 flex flex-col gap-y-5 2xl:gap-y-9">
          <div className="flex flex-col gap-y-1">
            <h1 className="text-lg 2xl:text-xl font-semibold">
              Create Product Set
            </h1>
            <p className="text-sm text-gray-500">
              Add detailed product information, set pricing, upload media, and
              configure inventory to start showcasing your product to potential
              buyers on Hubsups.
            </p>
          </div>
          <ProductStepper
            steps={steps}
            activeStep={activeStep}
            completedSteps={completedSteps}
            onStepClick={handleStepClick}
            pendingSections={pendingSections}
          />
          <ul className="flex gap-3 mt-auto text-xs text-gray-500">
            {/* <li className="relative after:content-['' after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-3">
                  <button
                    type="button"
                    className="text-xs font-semibold hover:text-primary-500 transition-base"
                    onClick={() => handleNextClick('Draft')}
                  >
                    Save as draft
                  </button>
                </li> */}
            <li className="relative">
              <Link
                href=""
                title="NeeD help?"
                className="text-xs font-semibold hover:text-primary-500 transition-base"
              >
                Need help?
              </Link>
            </li>
          </ul>
        </div>

        {/* Main content */}
        <div className="flex-1 w-full bg-surface-200 rounded-tl-xl rounded-bl-xl pt-8 2xl:pt-[52px] sm:ml-[250px] 2xl:ml-[352px] overflow-auto h-[calc(100vh-58px)]">
          {/* Step content */}
          {steps?.[activeStep]?.component}
        </div>
      </div>

      {/* Save draft modal */}
      <BaseModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Your progress has been saved!"
        paragraph=""
        size="md"
      >
        <div className="flex flex-col p-4">
          <p className="text-sm text-gray-500">
            We&apos;ve saved your product details, so you can continue listing
            it later without starting over.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={async () => {
              allowNavRef.current = true;
              await dispatch(resetProductState());
              await route.push('/products');
              allowNavRef.current = false;
              setIsModalOpen(false);
              setIsModalOpen(false);
            }}
          >
            Continue later
          </button>

          <button
            type="button"
            className="btn btn-primary"
            onClick={() => setIsModalOpen(false)}
          >
            Resume now
          </button>
        </div>
      </BaseModal>

      {/* modal for pending sections */}

      <BaseModal
        isOpen={showPopup}
        onClose={() => setShowPopup(false)}
        title="Leave page with unsaved changes?"
        paragraph=""
        size="md"
      >
        <div className="flex flex-col p-4">
          <p className="text-sm text-gray-500">
            Leaving this page will delete all the unsaved changes.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={async () => {
              setShowPopup(false);
              allowNavRef.current = true;
              if (nextUrlRef.current) {
                await route.push(nextUrlRef.current);
                await dispatch(resetProductState());
              }
            }}
          >
            Continue later
          </button>

          <button
            type="button"
            className="btn btn-primary"
            onClick={() => setShowPopup(false)}
          >
            Resume now
          </button>
        </div>
      </BaseModal>

      {/* Product Submitted Successfully */}
      <BaseModal
        isOpen={showSubmittedModal}
        onClose={() => setShowSubmittedModal(false)}
        size="md"
        title="Product Submitted Successfully"
      >
        <div className="p-4">
          <p className="text-sm text-gray-500">
            Your product listing has been submitted for review. Our team will
            review the details to ensure they meet platform guidelines. You will
            be notified once the product is approved and published on your
            store.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={() => {
              dispatch(resetProductState());
              setCompletedSteps([]);
              setShowSubmittedModal(false);
              setActiveStep(0);
            }}
          >
            Add Another Product Set
          </button>
          <button
            type="button"
            className="btn"
            onClick={async () => {
              allowNavRef.current = true;
              try {
                await dispatch(resetProductState());
                setShowSubmittedModal(false);
                await route.push('/products');
              } finally {
                allowNavRef.current = false;
              }
            }}
          >
            Return to Product List
          </button>
        </div>
      </BaseModal>
    </PrivateLayout>
  );
};

export default ProductSet;