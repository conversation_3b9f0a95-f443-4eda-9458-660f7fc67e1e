'use client';
import React, { useState } from 'react';
import InputField from '../Inputs/InputField';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import GlobalTableFilters from '../offCanvas/GlobalTableFilters';
// import SelectField from '../Inputs/SelectField';
import dynamic from 'next/dynamic';

const SelectField = dynamic(() => import('../Inputs/SelectField'), {
  ssr: false,
});

const FilterField = ({
  filterText,
  setFilterText,
  isSearchFilterOpen,
  setIsSearchFilterOpen,
  filters,
  setGlobalFilters,
  isDisplayMenuOpen,
  setIsDisplayMenuOpen,
  displayMenuRef,
  children,
  filterByStatus = false,
  statusTabs = null,
  activeStatusTab = null,
  setActiveStatusTab = null,
  placeholder = 'Search',
  status,
  setStatus,
}) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  return (
    <>
      {/* Filter field */}
      {!isSearchFilterOpen && (
        <div className="flex items-center justify-between gap-2.5 bg-white p-2 rounded-tl-xl rounded-tr-xl border border-border-color">
          <div className="flex items-center gap-2">
            <InputField
              type="search"
              placeholder={placeholder}
              value={filterText}
              marginBottom="mb-0 w-full max-w-[300px]"
              leftIcon="icon-search"
              onChange={(e) => setFilterText(e.target.value)}
              inputClassName="form-control !rounded-lg !min-h-[32px] !max-h-[32px] !py-1.5 !px-3"
            />

            {/* Status Tabs */}
            {statusTabs && statusTabs.length > 0 && !isSearchFilterOpen && (
              <div className="flex items-center gap-1 w-fit">
                {statusTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() =>
                      setActiveStatusTab && setActiveStatusTab(tab.id)
                    }
                    className={`flex items-center gap-1 px-3 py-1.5 rounded-md text-xs whitespace-nowrap font-medium transition-base ${activeStatusTab === tab.id
                        ? 'bg-gray-500/10 text-dark-500 hover:bg-gray-500/10 hover:text-dark-500'
                        : 'text-gray-400 hover:text-dark-500 hover:bg-gray-500/10'
                      }`}
                  >
                    {tab.label}
                    {tab.count !== null && (
                      <span
                        className={`w-4 h-4 inline-flex items-center justify-center px-1.5 py-0.5 text-[10px] rounded-full ${activeStatusTab === tab.id
                            ? 'bg-primary-100/80 text-primary-500'
                            : 'bg-gray-500/10 text-dark-500'
                          }`}
                      >
                        {tab.count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            )}
            {filterByStatus && (
              <SelectField
                label="Status"
                placeholder="Select status"
                name="status"
                className="single-select min-w-[150px] sm-select"
                value={status}
                onChange={(option) => setStatus(option?.value)}
                options={[
                  { value: 'all', label: 'All Status' },
                  { value: 'Active', label: 'Active' },
                  { value: 'Inactive', label: 'Inactive' },
                  { value: 'Pending', label: 'Pending' },
                  { value: 'Rejected', label: 'Rejected' },
                ]}
              />
            )}
          </div>

          {/* Table or Grid view option */}
          {/* <div className="flex items-center gap-2">
            <button
              onClick={() => setIsFilterOpen(true)}
              className="flex items-center gap-2 p-1.5 text-dark-500 rounded-lg border border-border-color hover:border-dark-500 cursor-pointer transition-base"
            >
              <span className="icon icon-funnel text-md" />
            </button>

            <div className="relative" ref={displayMenuRef}>
              <button
                onClick={() => setIsDisplayMenuOpen(!isDisplayMenuOpen)}
                className="flex items-center gap-2 p-1.5 rounded-lg border border-border-color hover:border-dark-500 cursor-pointer transition-base"
              >
                <span className="icon icon-square-split-horizontal text-dark-500 text-md" />
              </button>

              {isDisplayMenuOpen && children}
            </div>
          </div> */}
        </div>
      )}

      {/* Search and filter option */}
      {isSearchFilterOpen && (
        <div className="flex justify-between flex-col bg-white rounded-tl-xl rounded-tr-xl border border-border-color border-b-0">
          <div className="flex items-center justify-between gap-2 w-full border-b border-border-color p-2">
            <InputField
              type="text"
              placeholder="Search products"
              value={filterText}
              marginBottom="mb-0 w-full"
              leftIcon="icon-search"
              onChange={(e) => setFilterText(e.target.value)}
              inputClassName="w-full form-control !rounded-lg !min-h-[28px] !max-h-[28px] !py-1.5 !px-3"
            />

            <button
              onClick={() => {
                setIsSearchFilterOpen(false);
                setFilterText('');
              }}
              className="rounded-lg min-h-[28px] font-medium py-1.5 px-3 bg-transparent text-gray-400 text-xs hover:text-danger-500 hover:bg-danger-500/10 transition-base cursor-pointer"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
      <BaseOffCanvas
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Tickets"
        size="sm"
      >
        <GlobalTableFilters filters={filters} onChange={setGlobalFilters} />
      </BaseOffCanvas>
    </>
  );
};

export default FilterField;
