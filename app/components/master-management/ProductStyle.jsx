'use client';
import React, { useState, useCallback, useMemo } from 'react';
import MasterTable from '../table/MasterTable';
import BaseMasterModal from '../modals/BaseMasterModal';
import { Formik } from 'formik';
import { PRODUCT_STYLE_SCHEMA } from '@/utils/schema';

const ProductStyle = ({ isModalOpen, setIsModalOpen }) => {
  const [tableData, setTableData] = useState([]);
  const initialValues = useMemo(() => ({
    styleName: '',
    status: true,
  }), []);

  const handleDelete = useCallback((id) => {
    setTableData(prev => prev.filter(item => item.id !== id));
  }, []);

  const handleSubmit = useCallback((values, { resetForm }) => {
    const isDuplicate = tableData.some(
      item => item.styleName.toLowerCase() === values.styleName.toLowerCase()
    );

    if (isDuplicate) {
      alert('Style name already exists');
      return;
    }

    setTableData(prev => [
      ...prev,
      {
        id: Date.now(),
        styleName: values.styleName,
        status: values.status,
      },
    ]);

    resetForm();
    setIsModalOpen(false);
  }, [tableData, setIsModalOpen]);

  const columns = useMemo(() => [
    {
      name: 'Style Name',
      selector: row => row.styleName,
      sortable: true,
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold ${
          row.status ? 'bg-success-500/10 text-success-500' : 'bg-gray-500/10 text-gray-600'
        }`}>
          {row.status ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      sortable: false,
      grow: 0,
      cell: row => (
        <button
          className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          data-tooltip-id="delete-tooltip"
          data-tooltip-content="Delete"
          onClick={() => handleDelete(row.id)}
        >
          <span className="icon icon-trash text-base" />
        </button>
      ),
    },
  ], [handleDelete]);

  const fields = useMemo(() => [
    {
      type: 'text',
      name: 'styleName',
      label: 'Style Name',
      placeholder: 'Enter style name (e.g., Modern, Rustic)',
      required: true,
    },
    {
      type: 'toggle',
      name: 'status',
      label: 'Status',
    },
  ], []);

  return (
    <>
      <MasterTable columns={columns} data={tableData} />

      {isModalOpen && (
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validationSchema={PRODUCT_STYLE_SCHEMA()}
          onSubmit={handleSubmit}
        >
          {(formik) => (
            <BaseMasterModal
              isOpen={isModalOpen}
              onClose={() => {
                setIsModalOpen(false);
                formik.resetForm();
              }}
              title="Add Product Style"
              fields={fields}
              formik={formik}
            />
          )}
        </Formik>
      )}
    </>
  );
};

export default ProductStyle;