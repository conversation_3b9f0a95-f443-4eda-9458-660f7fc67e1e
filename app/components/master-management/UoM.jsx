import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import MasterTable from '../table/MasterTable';
import BaseMasterModal from '../modals/BaseMasterModal';
import { UNIT_OF_MASTER_SCHEMA } from '@/utils/schema';
import { Formik } from 'formik';
import { deleteUOMAsync, fetchAllUOMAsync } from '@/store/api/masterManagementApi';

const UoM = ({ isModalOpen, setIsModalOpen }) => {
  const dispatch = useDispatch();
  const [unitOfMeasure, setUnitOfMeasure] = useState([]);

  const fetchUnitOfMeasure = useCallback(async () => {
    try {
      const { data } = await dispatch(fetchAllUOMAsync()).unwrap();
      if (Array.isArray(data)) setUnitOfMeasure(data);
    } catch (error) {
      console.error('Failed to fetch UOM list:', error);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchUnitOfMeasure();
  }, [fetchUnitOfMeasure]);

  const handleDelete = useCallback(
    async (id) => {
      try {
        const { data } = await dispatch(deleteUOMAsync({ id })).unwrap();
        if (Array.isArray(data)) setUnitOfMeasure(data);
      } catch (error) {
        console.error('Failed to delete UOM:', error);
      }
    },
    [dispatch]
  );

  const handleSubmit = useCallback(
    (values, { resetForm }) => {
      const { code, fullName } = values;
      const codeLower = code.toLowerCase();
      const fullNameLower = fullName.toLowerCase();
      const timestamp = Date.now();

      const existingGroup = unitOfMeasure.find(
        (item) => item.name.toLowerCase() === codeLower
      );

      if (existingGroup) {
        const isDuplicate = existingGroup.children?.some(
          (child) => child.name.toLowerCase() === fullNameLower
        );
        if (isDuplicate) {
          alert('UoM already exists');
          return;
        }

        const updated = unitOfMeasure.map((item) =>
          item.id === existingGroup.id
            ? {
              ...item,
              children: [
                ...item.children,
                {
                  id: `${timestamp}`,
                  name: fullName,
                  parent_id: item.id,
                },
              ],
            }
            : item
        );
        setUnitOfMeasure(updated);
      } else {
        const newGroup = {
          id: `${timestamp}`,
          name: code,
          parent_id: null,
          children: [
            {
              id: `${timestamp}_child`,
              name: fullName,
              parent_id: null,
            },
          ],
        };
        setUnitOfMeasure((prev) => [...prev, newGroup]);
      }

      resetForm();
      setIsModalOpen(false);
    },
    [unitOfMeasure, setIsModalOpen]
  );

  const columns = useMemo(
    () => [
      {
        name: 'UoM Code',
        selector: (row) => row.code,
        sortable: true,
      },
      {
        name: 'Full Name',
        selector: (row) => row.name,
        sortable: true,
      },
      {
        name: 'Status',
        selector: (row) => row.status,
        sortable: true,
        cell: (row) => (
          <span
            className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold ${row.status
                ? 'bg-success-500/10 text-success-500'
                : 'bg-gray-500/10 text-gray-600'
              }`}
          >
            {row.status ? 'Active' : 'Inactive'}
          </span>
        ),
      },
      {
        name: 'Actions',
        sortable: false,
        grow: 0,
        cell: (row) => (
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => handleDelete(row.id)}
          >
            <span className="icon icon-trash text-base" />
          </button>
        ),
      },
    ],
    [handleDelete]
  );

  const fields = useMemo(
    () => [
      {
        type: 'text',
        name: 'code',
        label: 'UoM Code',
        placeholder: 'Enter UoM code (e.g., KG, PCS, LTR)',
        required: true,
      },
      {
        type: 'text',
        name: 'fullName',
        label: 'Full Name',
        placeholder: 'Enter full name (e.g., Kilogram)',
        required: true,
      },
      {
        type: 'toggle',
        name: 'status',
        label: 'Status',
      },
    ],
    []
  );

  return (
    <>
      <MasterTable columns={columns} data={unitOfMeasure} />

      <Formik
        initialValues={{ code: '', fullName: '', status: true }}
        validationSchema={UNIT_OF_MASTER_SCHEMA}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {(formik) => (
          <BaseMasterModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              formik.resetForm();
            }}
            title="Add Units of Measure"
            formik={formik}
            fields={fields}
          />
        )}
      </Formik>
    </>
  );
};

export default UoM;