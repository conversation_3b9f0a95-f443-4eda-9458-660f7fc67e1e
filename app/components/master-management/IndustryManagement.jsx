'use client';

import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Formik, Form } from 'formik';
import DataTable from 'react-data-table-component';
import { useDispatch } from 'react-redux';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import InputField from '../Inputs/InputField';
import ToggleSwitch from '../Inputs/ToggleSwitch';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';
import { Tooltip } from 'react-tooltip';
import { INDUSTRY_SCHEMA } from '../../../utils/schema';
import {
  createIndustryAsync,
  updateIndustryAsync,
  deleteIndustryAsync,
  fetchIndustriesAsync,
  fetchIdIndustriesAsync,
} from '@/store/api/masterManagementApi';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${rest.checked ? 'bg-primary-500 border-primary-500' : 'border-gray-200 hover:border-gray-100'
      }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const FormikToggleSwitch = ({ label, name, formik }) => (
  <div className="mb-0">
    {label && <label className="block text-sm font-medium mb-1">{label}</label>}
    <ToggleSwitch
      checked={formik.values[name]}
      onChange={(e) => formik.setFieldValue(name, e.target.checked)}
    />
  </div>
);

const IndustryManagement = ({
  isAddModalOpen,
  setIsAddModalOpen,
  initialFormValues,
  setInitialFormValues,
  editIndustryId,
  setEditIndustryId,
  filterText,
}) => {
  const dispatch = useDispatch();

  const [industries, setIndustries] = useState([]);
  const [selectedIndustries, setSelectedIndustries] = useState([]);
  const [sort, setSort] = useState({ field: null, direction: null });
  const [pagination, setPagination] = useState({ page: 1, perPage: 10, total: 0 });

  const resetFormState = useCallback(() => {
    setInitialFormValues({ industryName: '', status: true });
    setEditIndustryId(null);
    setIsAddModalOpen(false);
  }, []);

  const fetchIndustryList = async () => {
    const { payload } = await dispatch(
      fetchIndustriesAsync({ page: pagination.page, per_page: pagination.perPage })
    );
    if (payload?.data) {
      const filteredData = filterText
        ? payload.data.filter((industry) =>
          industry.name.toLowerCase().includes(filterText.toLowerCase())
        )
        : payload.data;

      setIndustries(filteredData);
      setPagination((prev) => ({
        ...prev,
        total: payload.meta?.total || 0,
      }));
    }
  };

  useEffect(() => {
    fetchIndustryList();
  }, [pagination.page, pagination.perPage, filterText]);

  const handleIndustrySubmit = useCallback(
    async (values, { resetForm }) => {
      const data = { name: values.industryName, status: values.status };
      const response = editIndustryId
        ? (await dispatch(updateIndustryAsync({ id: editIndustryId, ...data }))).payload
        : (await dispatch(createIndustryAsync(data))).payload;

      if (response?.ok || response?.status) {
        debouncedFetch(filterText, pagination.page, pagination.perPage);
        resetForm();
        resetFormState();
      } else {
        console.error('Submit Error:', response?.message);
      }
    },
    [dispatch, editIndustryId, resetFormState, filterText, pagination]
  );

  const handleDeleteIndustry = useCallback(
    async (industry) => {
      if (!window.confirm(`Are you sure you want to delete "${industry.name}"?`)) return;

      const { payload } = await dispatch(deleteIndustryAsync({ industryId: industry?.id }));
      if (payload?.ok || payload?.status) {
        debouncedFetch(filterText, pagination.page, pagination.perPage);
      } else {
        console.error(payload?.message || 'Failed to delete industry');
      }
    },
    [dispatch, filterText, pagination]
  );

  const handleEditIndustry = useCallback(
    async (industry) => {
      setIsAddModalOpen(false);
      const { payload } = await dispatch(fetchIdIndustriesAsync(industry?.id));
      if (payload?.data) {
        setInitialFormValues({
          industryName: payload.data.name,
          status: payload.data.status === 'Active',
        });
        setEditIndustryId(industry.id);
        setIsAddModalOpen(true);
      } else {
        resetFormState();
      }
    },
    [dispatch, resetFormState]
  );

  const handleSortChange = useCallback((column, direction) => {
    setSort({ field: column.selector, direction });
  }, []);

  const columns = useMemo(() => [
    {
      name: 'Industry Name',
      selector: (row) => row?.name || '-',
      sortable: true,
    },
    {
      name: 'Status',
      selector: (row) => row?.status || false,
      sortable: true,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${row.status === 'Active'
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
            }`}
        >
          {row.status === 'Active' ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      sortable: false,
      grow: 0,
      cell: (row) => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            onClick={() => handleEditIndustry(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <Tooltip
            id="edit-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            onClick={() => handleDeleteIndustry(row)}
            data-tooltip-id={`delete-tooltip-${row.id}`}
            data-tooltip-content="Delete"
          >
            <span className="icon icon-trash text-base" />
          </button>
          <Tooltip
            id={`delete-tooltip-${row.id}`}
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </>
      ),
    },
  ], [handleEditIndustry, handleDeleteIndustry]);

  const customTableStyles = useMemo(() => ({
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  }), []);

  return (
    <>
      <DataTable
        columns={columns}
        data={industries}
        customStyles={customTableStyles}
        pagination
        highlightOnHover
        selectableRows
        selectableRowsComponent={CustomCheckbox}
        onSelectedRowsChange={({ selectedRows }) => setSelectedIndustries(selectedRows)}
        sortIcon={<SortIcon sortDirection={sort.direction} />}
        onSort={handleSortChange}
        sortField={sort.field}
        defaultSortAsc
        paginationPerPage={pagination.perPage}
        selectableRowsHighlight
        paginationRowsPerPageOptions={[10]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          noRowsPerPage: true,
        }}
        paginationComponent={() => (
          <CommonPagination
            selectedCount={selectedIndustries.length}
            total={pagination.total}
            page={pagination.page}
            perPage={pagination.perPage}
            onPageChange={(page) => setPagination((prev) => ({ ...prev, page }))}
          />
        )}
      />

      <BaseOffCanvas
        isOpen={isAddModalOpen}
        onClose={resetFormState}
        size="sm"
        title={editIndustryId ? 'Edit Industry' : 'Add New Industry'}
      >
        <Formik
          enableReinitialize
          initialValues={initialFormValues}
          validationSchema={INDUSTRY_SCHEMA}
          onSubmit={handleIndustrySubmit}
        >
          {(formik) => (
            <Form>
              <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
                <InputField
                  name="industryName"
                  id="industryName"
                  label="Industry Name"
                  placeholder="Enter industry name"
                  formik={formik}
                  value={formik.values.industryName || ''}
                />
                <FormikToggleSwitch
                  name="status"
                  label="Status"
                  formik={formik}
                />
              </div>
              <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
                <button type="button" onClick={resetFormState} className="btn btn-outline-gray">
                  Cancel
                </button>
                <button type="submit" className="btn">
                  {editIndustryId ? 'Update Industry' : 'Add Industry'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </BaseOffCanvas>
    </>
  );
};

export default IndustryManagement;