'use client';
import React, { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import { fetchVendorAsync, approveRejectVendor } from '@/store/api/vendorsApi';
import { useDispatch } from 'react-redux';
import moment from 'moment';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
      }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const VendorDirectory = ({ filterText, status }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [selectedIndustries, setSelectedIndustries] = useState([]);
  const [debouncedText, setDebouncedText] = useState(filterText);

  // Sample vendor data
  const [vendors, setVendors] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    perPage: 10,
    total: 0,
  });

  const fetchVendorList = async () => {
    const { payload } = await dispatch(
      fetchVendorAsync({
        ...(debouncedText ? { 'filter[search]': debouncedText } : {}),
        ...(status && status !== 'all' && { status }),
        page: pagination.page,
        per_page: pagination.perPage,
      })
    );
    if (payload?.data) {
      const filteredData = payload.data;

      setVendors(filteredData);
      setPagination((prev) => ({
        ...prev,
        total: payload.meta?.total || 0,
      }));
    }
  };
  useEffect(() => {
    fetchVendorList();
  }, [pagination.page, pagination.perPage, status]);

   useEffect(() => {
      const timeout = setTimeout(() => {
        setDebouncedText(filterText);
      }, 2000);
      return () => clearTimeout(timeout);
    }, [filterText]);
  
    useEffect(() => {
      if (debouncedText) fetchVendorList();
    }, [debouncedText]);


  const handleApproveRejectVendor = async (vendorId, status, reason = null) => {
    try {
      const payload = {
        id: vendorId,
        status: status,
        ...(reason && { reason: reason })
      };
      const response = await dispatch(approveRejectVendor(payload)).unwrap();
      if (response?.payload?.status) {
        setVendors((prev) =>
          prev.map((vendor) =>
            vendor.id === vendorId ? { ...vendor, status: status === 'approved' ? 'Active' : 'Rejected' } : vendor
          )
        );
        fetchVendorList();
        return true;
      } else {
        throw new Error(response?.payload?.message || 'Failed to update vendor status');
      }
    } catch (error) {
      console.error('Error updating vendor status:', error);
      return false;
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      Active: 'bg-green-100 text-green-800',
      Inactive: 'bg-gray-100 text-gray-800',
      Pending: 'bg-yellow-100 text-yellow-800',
      Rejected: 'bg-red-100 text-red-800',
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[status] || 'bg-gray-100 text-gray-800'}`}
      >
        {status}
      </span>
    );
  };

  const handleAction = async (action, vendorId, vendorName) => {
    if (action === 'approve') {
      const result = await Swal.fire({
        title: 'Approve Vendor',
        html: `
          <div class="text-left">
            <p class="mb-2 text-sm">Are you sure you want to approve this vendor?</p>
            <div class="bg-surface-100 p-3 flex flex-col gap-1 rounded-lg">
              <p class="text-sm">Vendor ID: <strong>${vendorId}</strong></p>
              <p class="text-sm">Business Name: <strong>${vendorName}</strong></p>
            </div>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b857',
        cancelButtonColor: '#6B7280',
        confirmButtonText: 'Yes, Approve',
        cancelButtonText: 'Cancel',
        customClass: {
          popup: 'rounded-2xl',
          confirmButton: '!rounded-lg px-4 py-2',
          cancelButton: '!rounded-lg px-4 py-2',
        },
      });
      if (result.isConfirmed) {
        const success = await handleApproveRejectVendor(vendorId, 'approved', null);
        if (success) {
          Swal.fire({
            title: 'Approved!',
            text: `${vendorName} has been approved successfully.`,
            icon: 'success',
            timer: 2000,
            showConfirmButton: false,
            customClass: {
              popup: 'rounded-xl',
            },
          });
        }
      }
    } else if (action === 'reject') {
      const result = await Swal.fire({
        title: 'Reject Vendor',
        html: `
          <div class="text-left">
            <p class="mb-3 text-sm">Are you sure you want to reject this vendor?</p>
            <div class="text-left">
              <label class="block text-sm font-medium text-gray-700 mb-2">Reason for rejection:</label>
              <textarea 
                id="rejectionReason" 
                class="form-control" 
                rows="4" 
                placeholder="Please provide a reason for rejecting this vendor..."
              ></textarea>
            </div>
          </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#DC2626',
        cancelButtonColor: '#6B7280',
        confirmButtonText: 'Yes, Reject',
        cancelButtonText: 'Cancel',
        customClass: {
          popup: 'rounded-2xl',
          confirmButton: '!rounded-lg px-4 py-2',
          cancelButton: '!rounded-lg px-4 py-2',
        },
        preConfirm: () => {
          const reason = document.getElementById('rejectionReason').value.trim();
          if (!reason) {
            Swal.showValidationMessage('Please provide a reason for rejection');
            return false;
          }
          return reason;
        }
      });

      if (result.isConfirmed) {
        const rejectionReason = result.value;
        
        // Call API to reject the vendor
        const success = await handleApproveRejectVendor(vendorId, 'rejected', rejectionReason);
        
        if (success) {
          Swal.fire({
            title: 'Rejected!',
            text: `${vendorName} has been rejected successfully.`,
            icon: 'success',
            timer: 2000,
            showConfirmButton: false,
            customClass: {
              popup: 'rounded-xl',
            },
          });
        }
      }
    } else if (action === 'view') {
      // Navigate to vendor profile review page with vendor ID
      router.push(`/vendor-profile-review?vendorId=${vendorId}`);
    } else {
      // console.log(`${action} action for vendor:`, vendorId);
      // Implement other action logic here
    }
  };

  const columns = [
    {
      name: 'Vendor ID',
      selector: (row) => row?.vendor_code || '-',
      sortable: true,
      width: '100px',
      cell: (row) => (
        <span className="font-medium">{row?.vendor_code || '-'}</span>
      ),
    },
    {
      name: 'Business Name', //business name
      selector: (row) => row?.business_name || '-',
      sortable: true,
      width: '180px',
    },
    {
      name: 'Contact Person',
      selector: (row) =>
        row?.first_name?.toUpperCase() + row?.last_name?.toUpperCase() || '-',
      sortable: true,
      width: '150px',
      className: 'capitalize',
    },
    {
      name: 'Contact info',
      selector: (row) => row?.phone || '-',
      sortable: true,
      width: '200px',
      cell: (row) => (
        <div className="flex flex-col">
          <a
            href={`mailto:${row?.email}`}
            className="text-gray-400 hover:underline"
          >
            {row?.email || '-'}
          </a>
          <a
            href={`tel:${row?.phone}`}
            className="text-gray-400 text-xs hover:underline"
          >
            {row?.phone || '-'}
          </a>
        </div>
      ),
    },
    {
      name: 'Business Type',
      selector: (row) => row?.business_type || '-',
      sortable: true,
      // width: '120px',
    },
    {
      name: 'State',
      selector: (row) => row.state || '-',
      sortable: true,
      // width: '80px',
    },
    {
      name: 'Onboard',
      selector: (row) => row?.phone || '-',
      sortable: true,
      // width: '200px',
      cell: (row) => (
        <div className="flex flex-col">
          <span className="text-black-400">
            {row?.onboarding_status === 'Draft'
              ? 'Pending'
              : row?.onboarding_status === 'Completed'
                ? 'Pending Approval'
                : row?.onboarding_status || '-'}
          </span>
          <span className="text-gray-400 text-xs">Level: {row?.step}/4</span>
        </div>
      ),
    },
    {
      name: 'Status',
      selector: (row) => row.status || '-',
      sortable: true,
      // width: '100px',
      cell: (row) => getStatusBadge(row.status),
    },
    {
      name: 'Onboarded Date',
      selector: (row) => row.created_at || '-',
      sortable: true,
      // width: '130px',
      cell: (row) => moment(row?.created_at)?.format('MM/DD/YYYY'),
    },
    {
      name: 'Products',
      selector: (row) => row.productCount || '-', //change
      sortable: true,
      // width: '90px',
    },
    {
      name: 'Actions',
      sortable: false,
      cell: (row) => (
        <div className="flex items-center gap-1">
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Profile"
            onClick={() => handleAction('view', row.id)}
          >
            <span className="icon icon-eye text-base" />
          </button>

          {row.onboarding_status === 'Completed' && (
            <>
              <button
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base"
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                onClick={() => handleAction('approve', row.id, row?.business_name)}
              >
                <span className="icon icon-check-3 " />
              </button>
              <button
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                onClick={() => handleAction('reject', row.id, row?.business_name)}
              >
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}

          {/*     {row.status === 'Active' && (
            <button
              className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-orange-500/10 hover:text-orange-500 rounded-lg cursor-pointer transition-base"
              data-tooltip-id="suspend-tooltip"
              data-tooltip-content="Suspend"
              onClick={() => handleAction('suspend', row.id)}
            >
              <span className="icon icon-pause text-base" />
            </button>
          )}

          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => handleAction('delete', row.id)}
          >
            <span className="icon icon-trash text-base" />
          </button> */}

          <Tooltip
            id="view-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <Tooltip
            id="approve-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <Tooltip
            id="reject-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <Tooltip
            id="suspend-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <Tooltip
            id="delete-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </div>
      ),
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        // textTransform: 'uppercase',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const handleSelectedRowChange = ({ selectedRows }) => {
    setSelectedIndustries(selectedRows);
  };

  return (
    <DataTable
      columns={columns}
      data={vendors}
      customStyles={customStyles}
      highlightOnHover
      selectableRowsComponent={CustomCheckbox}
      className="custom-table"
      pagination
      selectableRows
      fixedHeader={true}
      sortIcon={<SortIcon sortDirection={sortDirection} />}
      onSort={handleSort}
      onSelectedRowsChange={handleSelectedRowChange}
      sortField={sortedField}
      defaultSortAsc={true}
      paginationPerPage={pagination.perPage}
      selectableRowsHighlight
      paginationRowsPerPageOptions={[10]}
      paginationComponentOptions={{
        rowsPerPageText: 'Rows per page:',
        rangeSeparatorText: 'of',
        noRowsPerPage: true,
      }}
      paginationComponent={() => (
        <CommonPagination
          selectedCount={selectedIndustries.length}
          total={pagination.total}
          page={pagination.page}
          perPage={pagination.perPage}
          onPageChange={(page) =>
            setPagination((prev) => ({
              ...prev,
              page,
            }))
          }
        />
      )}
    />
  );
};

export default VendorDirectory;
