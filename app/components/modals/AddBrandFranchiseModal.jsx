'use client';

import React from 'react';
import { Formik, Form } from 'formik';
import BaseOffCanvas from '@/app/components/offCanvas/BaseOffCanvas';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import ToggleSwitch from '@/app/components/Inputs/ToggleSwitch';
import ImageUpload from '@/app/components/Inputs/ImageUpload';
import { BRAND_SCHEMA, FRANCHISE_SCHEMA } from '@/utils/schema';

const AddBrandFranchiseModal = ({
  isOpen,
  onClose,
  selectedTab,
  isEditMode,
  editData,
  handleBrandSubmit,
  handleFranchiseSubmit,
  brandOptions = []
}) => {
  const isBrand = selectedTab === 0;

  const getInitialValues = React.useMemo(() => {
    if (isEditMode && editData) {
      if (isBrand) {
        return {
          brandName: editData?.brandName ?? '',
          logo: editData?.logo ?? [],
          status: typeof editData?.status === 'boolean' ? editData.status : true,
        };
      } else {
        return {
          franchiseName: editData?.franchiseName ?? '',
          parentBrand: editData?.parentBrand ?? '',
          status: typeof editData?.status === 'boolean' ? editData.status : true,
        };
      }
    }

    return isBrand
      ? {
        brandName: '',
        logo: [],
        status: true,
      }
      : {
        franchiseName: '',
        parentBrand: '',
        status: true,
      };
  }, [isEditMode, editData, isBrand]);


  return (
    <Formik
      enableReinitialize
      initialValues={getInitialValues}
      validationSchema={isBrand ? BRAND_SCHEMA : FRANCHISE_SCHEMA}
      onSubmit={isBrand ? handleBrandSubmit : handleFranchiseSubmit}
    >
      {(formik) => {
        const selectedParentBrandOption = formik.values.parentBrand
          ? brandOptions.find(opt => opt.value === formik.values.parentBrand)
          : null;

        return (
          <BaseOffCanvas
            isOpen={isOpen}
            onClose={onClose}
            size="sm"
            title={isEditMode ? (isBrand ? 'Edit Brand' : 'Edit Franchise') : (isBrand ? 'Add New Brand' : 'Add New Franchise')}
          >
            <Form>
              <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
                {isBrand ? (
                  <>
                    <InputField
                      type="text"
                      label="Brand Name"
                      placeholder="Enter brand name"
                      name="brandName"
                      id="brandName"
                      formik={formik}
                      value={formik.values.brandName}
                    />
                    <div className="mb-4">
                      <ImageUpload
                        name="logo"
                        placeholder="Upload image"
                        heading="Logo"
                        showFileInfo
                        formik={formik}
                        setImages={(images) => {
                          formik.setFieldValue('logo', images);
                          formik.setTouched({ logo: true });
                        }}
                        maxFiles={1}
                        maxSize={5000000}
                        uploadText="Add logo"
                        tooltipContent="Upload up to 1 file, up to 5MB, in .jpg, .jpeg, or .png format."
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-1">Status</label>
                      <ToggleSwitch
                        checked={formik.values.status}
                        onChange={(e) =>
                          formik.setFieldValue('status', e.target.checked)
                        }
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <InputField
                      label="Franchise Name"
                      placeholder="Enter franchise name"
                      name="franchiseName"
                      id="franchiseName"
                      formik={formik}
                      value={formik.values.franchiseName}
                    />
                    <div className="flex flex-col mb-4">
                      <label htmlFor="parentBrand" className="form-label">
                        Parent Brand <span className="text-danger-500">*</span>
                      </label>
                      <SelectField
                        className="single-select"
                        name="parentBrand"
                        placeholder="Select parent brand"
                        options={brandOptions}
                        formik={formik}
                        value={selectedParentBrandOption}
                        onChange={(option) => {
                          formik.setFieldValue('parentBrand', option ? option.value : '');
                          formik.setFieldTouched('parentBrand', true);
                        }}
                      />
                    </div>
                    <div className="mb-0">
                      <label className="block text-sm font-medium mb-1">Status</label>
                      <ToggleSwitch
                        checked={formik.values.status}
                        onChange={(e) =>
                          formik.setFieldValue('status', e.target.checked)
                        }
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
                <button
                  type="button"
                  onClick={() => {
                    formik.resetForm();
                    onClose();
                  }}
                  className="btn btn-outline-gray"
                >
                  Cancel
                </button>
                <button type="submit" className="btn">
                  {isEditMode ? (isBrand ? 'Update Brand' : 'Update Franchise') : (isBrand ? 'Add Brand' : 'Add Franchise')}
                </button>
              </div>
            </Form>
          </BaseOffCanvas>
        );
      }}
    </Formik>
  );
};

export default AddBrandFranchiseModal;