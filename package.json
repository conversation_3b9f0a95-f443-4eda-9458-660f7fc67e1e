{"name": "my-project", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev --turbopack --port=4100", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "format": "prettier --write ."}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "driver.js": "^1.3.5", "formik": "^2.4.6", "isomorphic-unfetch": "^4.0.2", "js-cookie": "^3.0.5", "lucide-react": "^0.482.0", "moment": "^2.30.1", "next": "15.2.2", "next-intl": "^4.1.0", "nextjs-toploader": "^3.7.15", "prop-types": "^15.8.1", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-data-table-component": "^7.7.0", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-google-recaptcha": "^3.1", "react-hook-form": "^7.54.2", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.0", "redux-persist": "^6.0.0", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "sweetalert2": "^11.22.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.2.2", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.2", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^15.5", "prettier": "^3.5.3", "tailwindcss": "^4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json}": ["prettier --write", "eslint --fix"]}}