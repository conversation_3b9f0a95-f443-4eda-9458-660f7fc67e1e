// All Master Management API

import { get, post, put, remove } from '@/api';
import { ADD_BRAND, ADD_FRANCHISE, ADD_INDUSTRY_MANAGEMENT, DELETE_BRAND, DELETE_FRANCHISE, DELETE_INDUSTRY_MANAGEMENT, DELETE_UOM, FETCH_INDUSTRY_MANAGEMENT, GET_FRANCHISE, GET_INDUSTRY_MANAGEMENT, GET_SINGLE_BRAND, LIST_ALL_BRAND, LIST_ALL_FRANCHISE, LIST_ALL_UOM, UPDATE_BRAND, UPDATE_FRANCHISE, UPDATE_INDUSTRY_MANAGEMENT } from '@/api/routes';
import { createAsyncThunk } from '@reduxjs/toolkit';

// Get All industries
export const fetchIndustriesAsync = createAsyncThunk(
  'industryManagement/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_INDUSTRY_MANAGEMENT, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Get industries (id)
export const fetchIdIndustriesAsync = createAsyncThunk(
  'industryManagement/fetchById',
  async (payload, thunkAPI) => {
    try {
      const response = await get(`${FETCH_INDUSTRY_MANAGEMENT}/${payload}`,{}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Update industries (id)
export const updateIndustryAsync = createAsyncThunk(
  'industryManagement/update',
  async ({ id, ...payload }, thunkAPI) => {
    try {
      const response = await put(`${UPDATE_INDUSTRY_MANAGEMENT}/${id}`, payload);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);


// Add Industries
export const createIndustryAsync = createAsyncThunk(
  'industryManagement/create',
  async (payload, thunkAPI) => {
    try {
      const response = await post(ADD_INDUSTRY_MANAGEMENT, payload, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Delete Industries
export const deleteIndustryAsync = createAsyncThunk(
  'industryManagement/delete',
  async ( payload , thunkAPI) => {
    const { industryId } = payload;
    console.log(`object`,`${DELETE_INDUSTRY_MANAGEMENT}/${industryId}`)
    try {
      const response = await remove(
        `${DELETE_INDUSTRY_MANAGEMENT}/${industryId}`,
        true,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// List All Brand
export const fetchAllBrandAsync = createAsyncThunk(
  'allBrand/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(LIST_ALL_BRAND, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Add Brand
export const createBrandAsync = createAsyncThunk(
  'brand/create',
  async (payload, thunkAPI) => {
    try {
      const response = await post(ADD_BRAND, payload, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Delete Brand
export const deleteBrandAsync = createAsyncThunk(
  'brand/delete',
  async ( payload , thunkAPI) => {
    const { brandId } = payload;
    try {
      const response = await remove(
        `${DELETE_BRAND}/${brandId}`,
        true,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Get Single Brand
export const fetchBrandByIdAsync = createAsyncThunk(
  'brand/fetchById',
  async (id, thunkAPI) => {
    try {
      const response = await get(`${GET_SINGLE_BRAND}/${id}`, {}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Update Brand by ID
export const updateBrandByIdAsync = createAsyncThunk(
  'brand/updateById',
  async ({ id, ...payload }, thunkAPI) => {
    try {
      const response = await put(`${UPDATE_BRAND}/${id}`, payload);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// List All Franchise
export const fetchAllFranchiseAsync = createAsyncThunk(
  'allFranchise/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(LIST_ALL_FRANCHISE, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Add Franchise
export const createFranchiseAsync = createAsyncThunk(
  'franchise/create',
  async (payload, thunkAPI) => {
    try {
      const response = await post(ADD_FRANCHISE, payload, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Delete Franchise
export const deleteFranchiseAsync = createAsyncThunk(
  'Franchise/delete',
  async ( payload , thunkAPI) => {
    const { franchiseId } = payload;
    console.log(`object`,`${DELETE_FRANCHISE}/${franchiseId}`)
    try {
      const response = await remove(
        `${DELETE_FRANCHISE}/${franchiseId}`,
        true,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Get Single Franchise
export const fetchFranchiseByIdAsync = createAsyncThunk(
  'franchise/fetchById',
  async (id, thunkAPI) => {
    try {
      const response = await get(`${GET_FRANCHISE}/${id}`, {}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Update Franchise by ID
export const updateFranchiseByIdAsync = createAsyncThunk(
  'franchise/updateById',
  async ({ id, ...payload }, thunkAPI) => {
    try {
      const response = await put(`${UPDATE_FRANCHISE}/${id}`, payload);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// List All Unit of Measure
export const fetchAllUOMAsync = createAsyncThunk(
  'allUoM/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(LIST_ALL_UOM, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// Delete Franchise
export const deleteUOMAsync = createAsyncThunk(
  'UOM/delete',
  async ( payload , thunkAPI) => {
    const { id } = payload;
    console.log(`object`,`${DELETE_UOM}/${id}`)
    try {
      const response = await remove(
        `${DELETE_UOM}/${id}`,
        true,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);